
import 'dart:developer';
import 'dart:ffi';

import 'package:dartz/dartz.dart';
import 'package:dorm/Features/home/<USER>/Model/dorms_model.dart';
import '../../../../../Core/Storage/Remote/response_model.dart';
import '../../../../Core/Storage/Remote/api_endpoints.dart';
import '../../../../Core/Storage/Remote/api_service.dart';
import 'repository_auth.dart';

class HomeRepositoryImpl implements HomeRepository {
  @override
  Future<Either<FailureModel, List<DormsModel>>> getAllDorms() async{
    try {
      final response = await DioHelper.getData(
        path: ApiEndpoints.getAllDorms,
      );
        log("response:: ${response.data['success']}");
      if (response.status) {
        final collection = response.data['data'];
        List<DormsModel> dorms = [];
        for (var element in collection) {
        dorms.add(DormsModel.fromJson(element)) ;
        
        }
        return Right(dorms);
      } else {
        return Left(
          FailureModel(message: response.message, error: response.data),
        );
      }
    } catch (e) {
      return Left(
        FailureModel(
          message: 'An unexpected error occurred during login: ${e.toString()}',
          error: e,
        ),
      );
    }

  }

  @override
  Future<Either<FailureModel, DormsModel>> getfavourites() {
    // TODO: implement getfavourites
    throw UnimplementedError();
  }

  @override
  Future<Either<FailureModel, DormsModel>> postfavourites() {
    // TODO: implement postfavourites
    throw UnimplementedError();
  }
 
}
