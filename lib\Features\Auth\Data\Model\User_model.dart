class UserModel {
    UserModel({
        required this.id,
        required this.username,
        required this.email,
        required this.createdAt,
        required this.profileImage,
        required this.role,
        required this.roleId,
        required this.isBanned,
    });

    final int id;
    final String username;
    final String email;
    final DateTime? createdAt;
    final dynamic profileImage;
    final String role;
    final dynamic roleId;
    final int isBanned;

    factory UserModel.fromJson(Map<String, dynamic> json){ 
        return UserModel(
            id: json["id"] ?? 0,
            username: json["username"] ?? "",
            email: json["email"] ?? "",
            createdAt: DateTime.tryParse(json["created_at"] ?? ""),
            profileImage: json["profile_image"],
            role: json["role"] ?? "",
            roleId: json["role_id"],
            isBanned: json["is_banned"] ?? 0,
        );
    }

    Map<String, dynamic> toJson() => {
        "id": id,
        "username": username,
        "email": email,
        "created_at": createdAt?.toIso8601String(),
        "profile_image": profileImage,
        "role": role,
        "role_id": roleId,
        "is_banned": isBanned,
    };

}
