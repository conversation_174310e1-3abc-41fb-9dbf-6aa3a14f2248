import 'package:bloc/bloc.dart';

import '../../home/<USER>/Model/dorms_model.dart';
import '../Repo/repository_impl_favorite.dart';

part 'favorite_state.dart';

class FavoriteCubit extends Cubit<FavoriteState> {
  FavoriteCubit(this.repositoryImplFavorite) : super(FavoriteState());
  final RepositoryImplFavorite repositoryImplFavorite;

  Future<void> getfavourites() async {
    emit(state.copyWith(isLoading: true));

    final response = await repositoryImplFavorite.getfavourites();
    response.fold(
      (l) => emit(state.copyWith(isLoading: false, message: l.message)),
      (r) => emit(state.copyWith(isLoading: false, dorms: r)),
    );
  }

  Future<void> postfavourites(int id) async {
    emit(state.copyWith(isLoading: true));

    // final response = await repositoryImplFavorite.postfavourites();
    // response.fold(
    //   (l) => emit(state.copyWith(isLoading: false, message: l.message)),
    //   (r) => emit(state.copyWith(isLoading: false, isFavourite: r)),
    // );


        emit(state.copyWith(isLoading: false, isFavourite: true,dorms: state.dorms!..removeWhere((element) => element.id == id))) ;
  }
}
