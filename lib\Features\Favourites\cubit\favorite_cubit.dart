import 'package:bloc/bloc.dart';

import '../../../Core/Services/local_favorites_service.dart';
import '../../home/<USER>/Model/dorms_model.dart';
import '../Repo/repository_impl_favorite.dart';

part 'favorite_state.dart';

class FavoriteCubit extends Cubit<FavoriteState> {
  FavoriteCubit(this.repositoryImplFavorite) : super(FavoriteState());
  final RepositoryImplFavorite repositoryImplFavorite;

  Future<void> getfavourites() async {
    emit(state.copyWith(isLoading: true));

    try {
      // First, get favorites from local storage
      final localFavorites = await LocalFavoritesService.getFavorites();

      // Emit local favorites immediately for better UX
      if (localFavorites.isNotEmpty) {
        emit(state.copyWith(isLoading: false, dorms: localFavorites));
      }

      // Then try to get from server and sync
      final response = await repositoryImplFavorite.getfavourites();
      response.fold(
        (l) {
          // If server fails but we have local data, keep local data
          if (localFavorites.isNotEmpty) {
            emit(
              state.copyWith(
                isLoading: false,
                message: 'Using offline favorites',
              ),
            );
          } else {
            emit(state.copyWith(isLoading: false, message: l.message));
          }
        },
        (r) {
          // Sync server data with local storage
          LocalFavoritesService.syncWithServer(r);
          emit(state.copyWith(isLoading: false, dorms: r));
        },
      );
    } catch (e) {
      // Fallback to local favorites if everything fails
      final localFavorites = await LocalFavoritesService.getFavorites();
      emit(
        state.copyWith(
          isLoading: false,
          dorms: localFavorites,
          message:
              localFavorites.isNotEmpty
                  ? 'Using offline favorites'
                  : 'Failed to load favorites',
        ),
      );
    }
  }

  Future<void> postfavourites(int id) async {
    emit(state.copyWith(isLoading: true));

    // final response = await repositoryImplFavorite.postfavourites();
    // response.fold(
    //   (l) => emit(state.copyWith(isLoading: false, message: l.message)),
    //   (r) => emit(state.copyWith(isLoading: false, isFavourite: r)),
    // );

    emit(
      state.copyWith(
        isLoading: false,
        isFavourite: true,
        dorms: state.dorms!..removeWhere((element) => element.id == id),
      ),
    );
  }
}
