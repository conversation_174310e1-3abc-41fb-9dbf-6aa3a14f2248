import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../Features/home/<USER>/Model/dorms_model.dart';

class LocalFavoritesService {
  static const String _favoritesKey = 'favorite_dorms';
  static const String _favoriteIdsKey = 'favorite_dorm_ids';

  /// Save a dorm to local favorites
  static Future<bool> addToFavorites(DormsModel dorm) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get current favorites
      final favorites = await getFavorites();
      final favoriteIds = await getFavoriteIds();
      
      // Check if already exists
      if (favoriteIds.contains(dorm.id)) {
        return true; // Already in favorites
      }
      
      // Add to lists
      favorites.add(dorm);
      favoriteIds.add(dorm.id);
      
      // Save to SharedPreferences
      final favoritesJson = favorites.map((dorm) => dorm.toJson()).toList();
      await prefs.setString(_favoritesKey, jsonEncode(favoritesJson));
      await prefs.setStringList(_favoriteIdsKey, favoriteIds.map((id) => id.toString()).toList());
      
      return true;
    } catch (e) {
      print('Error adding to favorites: $e');
      return false;
    }
  }

  /// Remove a dorm from local favorites
  static Future<bool> removeFromFavorites(int dormId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get current favorites
      final favorites = await getFavorites();
      final favoriteIds = await getFavoriteIds();
      
      // Remove from lists
      favorites.removeWhere((dorm) => dorm.id == dormId);
      favoriteIds.remove(dormId);
      
      // Save to SharedPreferences
      final favoritesJson = favorites.map((dorm) => dorm.toJson()).toList();
      await prefs.setString(_favoritesKey, jsonEncode(favoritesJson));
      await prefs.setStringList(_favoriteIdsKey, favoriteIds.map((id) => id.toString()).toList());
      
      return true;
    } catch (e) {
      print('Error removing from favorites: $e');
      return false;
    }
  }

  /// Get all favorite dorms from local storage
  static Future<List<DormsModel>> getFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesString = prefs.getString(_favoritesKey);
      
      if (favoritesString == null || favoritesString.isEmpty) {
        return [];
      }
      
      final List<dynamic> favoritesJson = jsonDecode(favoritesString);
      return favoritesJson.map((json) => DormsModel.fromJson(json)).toList();
    } catch (e) {
      print('Error getting favorites: $e');
      return [];
    }
  }

  /// Get favorite dorm IDs for quick lookup
  static Future<List<int>> getFavoriteIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoriteIdsString = prefs.getStringList(_favoriteIdsKey);
      
      if (favoriteIdsString == null || favoriteIdsString.isEmpty) {
        return [];
      }
      
      return favoriteIdsString.map((id) => int.parse(id)).toList();
    } catch (e) {
      print('Error getting favorite IDs: $e');
      return [];
    }
  }

  /// Check if a dorm is in favorites
  static Future<bool> isFavorite(int dormId) async {
    try {
      final favoriteIds = await getFavoriteIds();
      return favoriteIds.contains(dormId);
    } catch (e) {
      print('Error checking if favorite: $e');
      return false;
    }
  }

  /// Clear all favorites
  static Future<bool> clearFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_favoritesKey);
      await prefs.remove(_favoriteIdsKey);
      return true;
    } catch (e) {
      print('Error clearing favorites: $e');
      return false;
    }
  }

  /// Get favorites count
  static Future<int> getFavoritesCount() async {
    try {
      final favoriteIds = await getFavoriteIds();
      return favoriteIds.length;
    } catch (e) {
      print('Error getting favorites count: $e');
      return 0;
    }
  }

  /// Sync local favorites with server favorites
  static Future<void> syncWithServer(List<DormsModel> serverFavorites) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Save server favorites to local storage
      final favoritesJson = serverFavorites.map((dorm) => dorm.toJson()).toList();
      final favoriteIds = serverFavorites.map((dorm) => dorm.id).toList();
      
      await prefs.setString(_favoritesKey, jsonEncode(favoritesJson));
      await prefs.setStringList(_favoriteIdsKey, favoriteIds.map((id) => id.toString()).toList());
    } catch (e) {
      print('Error syncing with server: $e');
    }
  }
}
